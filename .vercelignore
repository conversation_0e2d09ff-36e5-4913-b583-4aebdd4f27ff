# Test files
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
**/test/**
**/__tests__/**

# Only ignore compiled JS files in config, not the TypeScript source
api/config/**/*.js
api/config/**/*.mjs

# Note: We need handlers, middleware, services, utils, and types for imports
# Only ignore specific files that shouldn't be serverless functions
api/handlers/**/*.js
api/handlers/**/*.mjs

# Development files
*.md
*.txt
.env*
.git*
node_modules/
dist/
build/

# Documentation
docs/
*.md
