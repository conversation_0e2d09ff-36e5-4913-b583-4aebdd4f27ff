// import React from 'react'; // Not needed for this component
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12',
};

export function LoadingSpinner({ size = 'md', className, text }: LoadingSpinnerProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center gap-2', className)}>
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      {text && <p className='text-sm text-muted-foreground animate-pulse'>{text}</p>}
    </div>
  );
}

interface PageLoadingProps {
  title?: string;
  description?: string;
  className?: string;
}

export function PageLoading({ title = 'Loading...', description, className }: PageLoadingProps) {
  return (
    <div
      className={cn('flex flex-col items-center justify-center min-h-[400px] gap-4 p-8', className)}
    >
      <LoadingSpinner size='lg' />
      <div className='text-center space-y-2'>
        <h3 className='text-lg font-semibold text-foreground'>{title}</h3>
        {description && <p className='text-sm text-muted-foreground max-w-md'>{description}</p>}
      </div>
    </div>
  );
}

interface ComponentLoadingProps {
  name?: string;
  className?: string;
}

export function ComponentLoading({ name = 'component', className }: ComponentLoadingProps) {
  return (
    <div
      className={cn(
        'flex items-center justify-center p-4 rounded-lg border border-dashed border-border bg-muted/50',
        className
      )}
    >
      <LoadingSpinner size='sm' text={`Loading ${name}...`} />
    </div>
  );
}

// Skeleton loading components for better UX
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn('rounded-lg border bg-card p-4 space-y-3', className)}>
      <div className='h-4 bg-muted rounded animate-pulse' />
      <div className='space-y-2'>
        <div className='h-3 bg-muted rounded animate-pulse w-3/4' />
        <div className='h-3 bg-muted rounded animate-pulse w-1/2' />
      </div>
    </div>
  );
}

export function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn('h-3 bg-muted rounded animate-pulse', i === lines - 1 ? 'w-2/3' : 'w-full')}
        />
      ))}
    </div>
  );
}
