export const translations = {
  en: {
    api: {
      title: 'API Documentation',
      subtitle: 'Convert timestamps, dates, and timezones with our powerful REST API',
      overview: 'Overview',
      endpoints: 'Endpoints',
      testing: 'Testing',
      formats: 'Formats',
      examples: 'Examples',
      monitoring: 'Monitoring',
      quickStart: 'Quick Start',
      bestPractices: 'Best Practices',
      support: 'Support',
      convertDescription: 'Convert Unix timestamps to human-readable formats and vice versa',
    }
  },
  zh: {
    api: {
      title: 'API 文档',
      subtitle: '使用我们强大的 REST API 转换时间戳、日期和时区',
      overview: '概览',
      endpoints: '端点',
      testing: '测试',
      formats: '格式',
      examples: '示例',
      monitoring: '监控',
      quickStart: '快速开始',
      bestPractices: '最佳实践',
      support: '支持',
      convertDescription: '将 Unix 时间戳转换为可读格式，反之亦然',
    }
  }
};