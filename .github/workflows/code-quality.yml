name: 🔍 Code Quality

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ main, develop ]
  schedule:
    # Run weekly code quality checks
    - cron: '0 6 * * 1'

jobs:
  # ============================================================================
  # Static Analysis
  # ============================================================================
  
  static-analysis:
    name: 📊 Static Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: 🔧 Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: 🔍 ESLint analysis
        run: |
          npx eslint . --ext .ts,.tsx,.js,.jsx --format json --output-file eslint-report.json || true
          npx eslint . --ext .ts,.tsx,.js,.jsx --format stylish
      
      - name: 📊 Upload ESLint results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: eslint-report
          path: eslint-report.json
      
      - name: 🎯 TypeScript strict check
        run: npx tsc --noEmit --strict
      
      - name: 📏 Code complexity analysis
        run: |
          echo "## 📏 Code Complexity Analysis" >> $GITHUB_STEP_SUMMARY
          npx complexity-report --format json --output complexity-report.json src/ || true
          echo "Complexity report generated" >> $GITHUB_STEP_SUMMARY

  # ============================================================================
  # Code Coverage
  # ============================================================================
  
  coverage:
    name: 📊 Code Coverage
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: 🔧 Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: 🧪 Run tests with coverage
        run: npm run test:coverage
        env:
          CI: true
      
      - name: 📊 Generate coverage report
        run: |
          echo "## 📊 Code Coverage Report" >> $GITHUB_STEP_SUMMARY
          npx nyc report --reporter=text-summary >> $GITHUB_STEP_SUMMARY
      
      - name: 📤 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false
      
      - name: 📊 Coverage comment
        uses: romeovs/lcov-reporter-action@v0.3.1
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          lcov-file: ./coverage/lcov.info

  # ============================================================================
  # Dependency Analysis
  # ============================================================================
  
  dependency-analysis:
    name: 📦 Dependency Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: 🔧 Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: 🔍 Dependency vulnerability scan
        run: |
          npm audit --audit-level=moderate --json > audit-report.json || true
          echo "## 🔍 Dependency Vulnerability Scan" >> $GITHUB_STEP_SUMMARY
          npm audit --audit-level=moderate >> $GITHUB_STEP_SUMMARY || true
      
      - name: 📊 Bundle analysis
        run: |
          npm run analyze-bundle
          echo "## 📦 Bundle Analysis" >> $GITHUB_STEP_SUMMARY
          echo "Bundle analysis completed" >> $GITHUB_STEP_SUMMARY
      
      - name: 🔍 License compliance check
        run: |
          npx license-checker --json --out license-report.json
          echo "## 📄 License Compliance" >> $GITHUB_STEP_SUMMARY
          npx license-checker --summary >> $GITHUB_STEP_SUMMARY
      
      - name: 📤 Upload reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: dependency-reports
          path: |
            audit-report.json
            license-report.json

  # ============================================================================
  # Performance Analysis
  # ============================================================================
  
  performance-analysis:
    name: ⚡ Performance Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: 🔧 Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: 🏗️ Build application
        run: npm run build
        env:
          NODE_ENV: production
      
      - name: 📊 Bundle size analysis
        run: |
          echo "## 📊 Bundle Size Analysis" >> $GITHUB_STEP_SUMMARY
          npm run analyze-code-splitting >> $GITHUB_STEP_SUMMARY
      
      - name: 🖼️ Image optimization check
        run: |
          echo "## 🖼️ Image Optimization" >> $GITHUB_STEP_SUMMARY
          npm run analyze-images >> $GITHUB_STEP_SUMMARY
      
      - name: 🚀 Cache strategy analysis
        run: |
          echo "## 🚀 Cache Strategy" >> $GITHUB_STEP_SUMMARY
          npm run analyze-cache >> $GITHUB_STEP_SUMMARY

  # ============================================================================
  # Code Quality Gates
  # ============================================================================
  
  quality-gate:
    name: 🚪 Quality Gate
    runs-on: ubuntu-latest
    needs: [static-analysis, coverage, dependency-analysis, performance-analysis]
    if: always()
    timeout-minutes: 5
    
    steps:
      - name: 📊 Evaluate quality metrics
        run: |
          echo "## 🚪 Quality Gate Results" >> $GITHUB_STEP_SUMMARY
          
          # Check if all jobs passed
          if [[ "${{ needs.static-analysis.result }}" == "success" && \
                "${{ needs.coverage.result }}" == "success" && \
                "${{ needs.dependency-analysis.result }}" == "success" && \
                "${{ needs.performance-analysis.result }}" == "success" ]]; then
            echo "✅ **Quality Gate: PASSED**" >> $GITHUB_STEP_SUMMARY
            echo "All quality checks have passed successfully." >> $GITHUB_STEP_SUMMARY
            exit 0
          else
            echo "❌ **Quality Gate: FAILED**" >> $GITHUB_STEP_SUMMARY
            echo "One or more quality checks have failed:" >> $GITHUB_STEP_SUMMARY
            echo "- Static Analysis: ${{ needs.static-analysis.result }}" >> $GITHUB_STEP_SUMMARY
            echo "- Coverage: ${{ needs.coverage.result }}" >> $GITHUB_STEP_SUMMARY
            echo "- Dependency Analysis: ${{ needs.dependency-analysis.result }}" >> $GITHUB_STEP_SUMMARY
            echo "- Performance Analysis: ${{ needs.performance-analysis.result }}" >> $GITHUB_STEP_SUMMARY
            exit 1
          fi
      
      - name: 📝 Quality gate comment
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          script: |
            const qualityPassed = '${{ needs.static-analysis.result }}' === 'success' &&
                                 '${{ needs.coverage.result }}' === 'success' &&
                                 '${{ needs.dependency-analysis.result }}' === 'success' &&
                                 '${{ needs.performance-analysis.result }}' === 'success';
            
            const status = qualityPassed ? '✅ PASSED' : '❌ FAILED';
            const emoji = qualityPassed ? '🎉' : '⚠️';
            
            const body = `${emoji} **Quality Gate: ${status}**
            
            | Check | Status |
            |-------|--------|
            | Static Analysis | ${{ needs.static-analysis.result }} |
            | Code Coverage | ${{ needs.coverage.result }} |
            | Dependency Analysis | ${{ needs.dependency-analysis.result }} |
            | Performance Analysis | ${{ needs.performance-analysis.result }} |
            
            ${qualityPassed ? 'All quality checks have passed! 🚀' : 'Please review and fix the failing checks before merging.'}`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: body
            });
