name: 🚀 Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.2.3)'
        required: true
        type: string
      prerelease:
        description: 'Is this a pre-release?'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'

jobs:
  # ============================================================================
  # Validate Release
  # ============================================================================
  
  validate:
    name: 🔍 Validate Release
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      is-prerelease: ${{ steps.version.outputs.is-prerelease }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 🏷️ Extract version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            VERSION="${{ github.event.inputs.version }}"
            IS_PRERELEASE="${{ github.event.inputs.prerelease }}"
          else
            VERSION="${GITHUB_REF#refs/tags/}"
            IS_PRERELEASE="false"
            if [[ "$VERSION" =~ -[a-zA-Z] ]]; then
              IS_PRERELEASE="true"
            fi
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "is-prerelease=$IS_PRERELEASE" >> $GITHUB_OUTPUT
          echo "Release version: $VERSION"
          echo "Is pre-release: $IS_PRERELEASE"
      
      - name: 📋 Validate version format
        run: |
          VERSION="${{ steps.version.outputs.version }}"
          if [[ ! "$VERSION" =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9.-]+)?$ ]]; then
            echo "❌ Invalid version format: $VERSION"
            echo "Expected format: v1.2.3 or v1.2.3-alpha.1"
            exit 1
          fi
          echo "✅ Version format is valid"

  # ============================================================================
  # Build Release
  # ============================================================================
  
  build:
    name: 🏗️ Build Release
    runs-on: ubuntu-latest
    needs: [validate]
    timeout-minutes: 20
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: 🔧 Install dependencies
        run: npm ci --prefer-offline --no-audit
      
      - name: 🧪 Run tests
        run: npm run test
        env:
          CI: true
      
      - name: 🖼️ Optimize images
        run: npm run optimize-images
      
      - name: 🏗️ Build application
        run: npm run build
        env:
          NODE_ENV: production
          VITE_VERSION: ${{ needs.validate.outputs.version }}
          VITE_BUILD_TIME: ${{ github.run_number }}
          VITE_COMMIT_SHA: ${{ github.sha }}
      
      - name: 📊 Generate build report
        run: |
          echo "## 🏗️ Build Report" > build-report.md
          echo "- **Version**: ${{ needs.validate.outputs.version }}" >> build-report.md
          echo "- **Build Time**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")" >> build-report.md
          echo "- **Commit**: ${{ github.sha }}" >> build-report.md
          echo "- **Bundle Size**: $(du -sh dist | cut -f1)" >> build-report.md
          echo "" >> build-report.md
          echo "### 📊 Bundle Analysis" >> build-report.md
          npm run analyze-code-splitting >> build-report.md
      
      - name: 📦 Create release archive
        run: |
          cd dist
          tar -czf ../tsconv-${{ needs.validate.outputs.version }}.tar.gz .
          cd ..
          zip -r tsconv-${{ needs.validate.outputs.version }}.zip dist/
      
      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: release-${{ needs.validate.outputs.version }}
          path: |
            dist/
            tsconv-${{ needs.validate.outputs.version }}.tar.gz
            tsconv-${{ needs.validate.outputs.version }}.zip
            build-report.md
          retention-days: 90

  # ============================================================================
  # Security Scan
  # ============================================================================
  
  security-scan:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    needs: [validate, build]
    timeout-minutes: 15
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: release-${{ needs.validate.outputs.version }}
      
      - name: 🔍 Security vulnerability scan
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: './dist'
          format: 'json'
          output: 'security-report.json'
      
      - name: 📊 Generate security report
        run: |
          echo "## 🔒 Security Scan Report" > security-report.md
          echo "Security scan completed for release ${{ needs.validate.outputs.version }}" >> security-report.md
          
          if [[ -f security-report.json ]]; then
            CRITICAL=$(jq '.Results[]?.Vulnerabilities[]? | select(.Severity=="CRITICAL") | length' security-report.json | wc -l)
            HIGH=$(jq '.Results[]?.Vulnerabilities[]? | select(.Severity=="HIGH") | length' security-report.json | wc -l)
            
            echo "- **Critical vulnerabilities**: $CRITICAL" >> security-report.md
            echo "- **High vulnerabilities**: $HIGH" >> security-report.md
            
            if [[ $CRITICAL -gt 0 ]]; then
              echo "❌ Critical vulnerabilities found! Release blocked." >> security-report.md
              exit 1
            fi
          fi
      
      - name: 📤 Upload security report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-report-${{ needs.validate.outputs.version }}
          path: |
            security-report.json
            security-report.md

  # ============================================================================
  # Create Release
  # ============================================================================
  
  create-release:
    name: 📋 Create Release
    runs-on: ubuntu-latest
    needs: [validate, build, security-scan]
    timeout-minutes: 10
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: release-${{ needs.validate.outputs.version }}
      
      - name: 📝 Generate changelog
        id: changelog
        run: |
          # Get the previous tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [[ -n "$PREVIOUS_TAG" ]]; then
            echo "## 📋 Changelog" > changelog.md
            echo "" >> changelog.md
            git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD >> changelog.md
          else
            echo "## 📋 Initial Release" > changelog.md
            echo "" >> changelog.md
            echo "This is the initial release of TSConv." >> changelog.md
          fi
          
          # Add build information
          echo "" >> changelog.md
          cat build-report.md >> changelog.md
          
          # Set output for release body
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          cat changelog.md >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      
      - name: 🚀 Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.validate.outputs.version }}
          release_name: Release ${{ needs.validate.outputs.version }}
          body: ${{ steps.changelog.outputs.changelog }}
          draft: false
          prerelease: ${{ needs.validate.outputs.is-prerelease }}
      
      - name: 📤 Upload release assets
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./tsconv-${{ needs.validate.outputs.version }}.tar.gz
          asset_name: tsconv-${{ needs.validate.outputs.version }}.tar.gz
          asset_content_type: application/gzip
      
      - name: 📤 Upload release ZIP
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./tsconv-${{ needs.validate.outputs.version }}.zip
          asset_name: tsconv-${{ needs.validate.outputs.version }}.zip
          asset_content_type: application/zip

  # ============================================================================
  # Deploy Release
  # ============================================================================
  
  deploy:
    name: 🌟 Deploy Release
    runs-on: ubuntu-latest
    needs: [validate, create-release]
    environment: production
    timeout-minutes: 20
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
      
      - name: 📦 Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: release-${{ needs.validate.outputs.version }}
      
      - name: 🚀 Deploy to production
        run: |
          echo "🚀 Deploying release ${{ needs.validate.outputs.version }} to production"
          # Add your production deployment commands here
          # This could include deploying to Vercel, Cloudflare, AWS, etc.
      
      - name: 📊 Post-deployment verification
        run: |
          echo "✅ Release ${{ needs.validate.outputs.version }} deployed successfully"
          echo "🔗 Production URL: https://tsconv.com"
      
      - name: 🎉 Release notification
        run: |
          echo "## 🎉 Release ${{ needs.validate.outputs.version }} Deployed!" >> $GITHUB_STEP_SUMMARY
          echo "- **Version**: ${{ needs.validate.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Production URL**: https://tsconv.com" >> $GITHUB_STEP_SUMMARY
          echo "- **Release Notes**: https://github.com/${{ github.repository }}/releases/tag/${{ needs.validate.outputs.version }}" >> $GITHUB_STEP_SUMMARY
