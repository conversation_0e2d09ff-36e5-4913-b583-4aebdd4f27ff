version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "tsconv-team"
    assignees:
      - "tsconv-maintainer"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"
    ignore:
      # Ignore major version updates for critical dependencies
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "vite"
        update-types: ["version-update:semver-major"]
    groups:
      # Group React ecosystem updates
      react-ecosystem:
        patterns:
          - "react*"
          - "@types/react*"
      # Group build tools
      build-tools:
        patterns:
          - "vite*"
          - "@vitejs/*"
          - "rollup*"
          - "esbuild*"
      # Group testing tools
      testing:
        patterns:
          - "vitest*"
          - "@testing-library/*"
          - "jest*"
          - "cypress*"
      # Group linting tools
      linting:
        patterns:
          - "eslint*"
          - "@typescript-eslint/*"
          - "prettier*"
      # Group TypeScript
      typescript:
        patterns:
          - "typescript"
          - "@types/*"

  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "10:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "github-actions"
      - "automated"

  # Enable version updates for Docker (if we add Dockerfile later)
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    commit-message:
      prefix: "docker"
      include: "scope"
    labels:
      - "docker"
      - "automated"
