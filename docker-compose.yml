# TSConv Docker Compose Configuration
# This file defines the complete application stack for development and production

version: '3.8'

services:
  # ============================================================================
  # Main Application
  # ============================================================================
  
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    image: tsconv:latest
    container_name: tsconv-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
    volumes:
      # Mount logs for monitoring
      - ./logs/nginx:/var/log/nginx
      # Mount custom nginx config if needed
      # - ./docker/custom-nginx.conf:/etc/nginx/conf.d/custom.conf:ro
    networks:
      - tsconv-network
    healthcheck:
      test: ["/usr/local/bin/health-check.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tsconv.rule=Host(`tsconv.local`)"
      - "traefik.http.services.tsconv.loadbalancer.server.port=8080"
      - "com.docker.compose.service=tsconv-app"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # ============================================================================
  # Reverse Proxy (Optional - for production)
  # ============================================================================
  
  proxy:
    image: traefik:v2.10
    container_name: tsconv-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8081:8080"  # Traefik dashboard
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --log.level=INFO
      - --accesslog=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./data/letsencrypt:/letsencrypt
      - ./logs/traefik:/var/log/traefik
    networks:
      - tsconv-network
    depends_on:
      - app
    profiles:
      - production
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.tsconv.local`)"
      - "traefik.http.routers.dashboard.service=api@internal"

  # ============================================================================
  # Monitoring (Optional)
  # ============================================================================
  
  prometheus:
    image: prom/prometheus:latest
    container_name: tsconv-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - tsconv-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: tsconv-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - tsconv-network
    depends_on:
      - prometheus
    profiles:
      - monitoring

  # ============================================================================
  # Log Management (Optional)
  # ============================================================================
  
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: tsconv-fluentd
    restart: unless-stopped
    volumes:
      - ./logs:/fluentd/log
      - ./monitoring/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    networks:
      - tsconv-network
    profiles:
      - logging

# ============================================================================
# Networks
# ============================================================================

networks:
  tsconv-network:
    driver: bridge
    name: tsconv-network

# ============================================================================
# Volumes
# ============================================================================

volumes:
  prometheus-data:
    driver: local
    name: tsconv-prometheus-data
  
  grafana-data:
    driver: local
    name: tsconv-grafana-data
