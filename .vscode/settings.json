{"editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.tabSize": 2, "editor.insertSpaces": true, "editor.rulers": [100], "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.enable": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.enable": true, "prettier.requireConfig": true, "typescript.format.enable": false, "javascript.format.enable": false, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.DS_Store": true, "**/coverage": true}, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "files.autoSave": "onFocusChange"}