{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "useUnknownInCatchVariables": true, "alwaysStrict": true, "strictBindCallApply": true, "strictFunctionTypes": true, "strictNullChecks": true, "strictPropertyInitialization": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@api/*": ["./api/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["src", "api"], "exclude": ["**/*.test.ts", "**/*.spec.ts", "node_modules"], "references": [{"path": "./tsconfig.node.json"}]}