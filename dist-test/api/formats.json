{"success": true, "data": {"formats": [{"name": "ISO 8601", "value": "iso8601", "description": "International standard date format", "example": "2024-01-15T14:30:00.000Z", "category": "standard"}, {"name": "UTC String", "value": "utc", "description": "UTC date string format", "example": "Mon, 15 Jan 2024 14:30:00 GMT", "category": "standard"}, {"name": "Unix Timestamp", "value": "timestamp", "description": "Seconds since Unix epoch", "example": "1705321800", "category": "technical"}, {"name": "Local String", "value": "local", "description": "Browser's local format", "example": "1/15/2024, 2:30:00 PM", "category": "standard"}, {"name": "Relative Time", "value": "relative", "description": "Human-readable relative time", "example": "2 hours ago", "category": "human"}, {"name": "Short Date", "value": "short", "description": "Short date format", "example": "01/15/2024", "category": "standard"}, {"name": "Time Only", "value": "time", "description": "Time without date", "example": "2:30:00 PM", "category": "standard"}, {"name": "RFC 2822", "value": "rfc2822", "description": "Email date format", "example": "Mon, 15 Jan 2024 14:30:00 +0000", "category": "standard"}, {"name": "US DateTime", "value": "us-datetime", "description": "US format with date and time", "example": "01/15/2024 02:30:00 PM", "category": "regional"}, {"name": "EU DateTime", "value": "eu-datetime", "description": "European format with date and time", "example": "15/01/2024 14:30:00", "category": "regional"}, {"name": "MySQL DateTime", "value": "mysql", "description": "MySQL DATETIME format", "example": "2024-01-15 14:30:00", "category": "technical"}, {"name": "File Name Safe", "value": "filename", "description": "Safe for file names", "example": "2024-01-15_14-30-00", "category": "technical"}, {"name": "Day of Week", "value": "day-of-week", "description": "Full day name", "example": "Monday", "category": "human"}, {"name": "Month Name", "value": "month-name", "description": "Full month name", "example": "January", "category": "human"}, {"name": "Quarter", "value": "quarter", "description": "Quarter of the year", "example": "Q1 2024", "category": "human"}, {"name": "Week Number", "value": "week-number", "description": "ISO week number", "example": "2024-W03", "category": "technical"}, {"name": "Ordinal Date", "value": "ordinal", "description": "Day of year", "example": "2024-015", "category": "technical"}, {"name": "Chinese Date", "value": "chinese", "description": "Chinese calendar format", "example": "2024年1月15日", "category": "regional"}, {"name": "Japanese Date", "value": "japanese", "description": "Japanese calendar format", "example": "2024年1月15日", "category": "regional"}, {"name": "Korean Date", "value": "korean", "description": "Korean calendar format", "example": "2024년 1월 15일", "category": "regional"}], "categories": ["standard", "human", "regional", "technical"], "total": 20}, "meta": {"timestamp": "2024-01-15T14:30:00.000Z", "cacheHit": true}}