# TSConv - Optimized Cache Headers Configuration
# This .htaccess file provides comprehensive caching strategies for different asset types

# Enable compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Enable Gzip compression
<IfModule mod_gzip.c>
    mod_gzip_on Yes
    mod_gzip_dechunk Yes
    mod_gzip_item_include file \.(html?|txt|css|js|php|pl)$
    mod_gzip_item_include handler ^cgi-script$
    mod_gzip_item_include mime ^text/.*
    mod_gzip_item_include mime ^application/x-javascript.*
    mod_gzip_item_exclude mime ^image/.*
    mod_gzip_item_exclude rspheader ^Content-Encoding:.*gzip.*
</IfModule>

# Cache Control Headers
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Default expiration: 1 hour
    ExpiresDefault "access plus 1 hour"
    
    # HTML files: 1 hour (for dynamic content)
    ExpiresByType text/html "access plus 1 hour"
    
    # CSS and JavaScript with hash: 1 year (immutable)
    <FilesMatch "\.(css|js)$">
        <If "%{REQUEST_URI} =~ /-[a-f0-9]{8,}\.(css|js)$/">
            ExpiresDefault "access plus 1 year"
            Header set Cache-Control "public, immutable"
        </If>
        <Else>
            ExpiresDefault "access plus 1 hour"
            Header set Cache-Control "public, must-revalidate"
        </Else>
    </FilesMatch>
    
    # Images: 1 year
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/avif "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts: 1 year
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"
    ExpiresByType application/x-font-opentype "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    
    # JSON data: 1 day
    ExpiresByType application/json "access plus 1 day"
    
    # XML files: 1 day
    ExpiresByType application/xml "access plus 1 day"
    ExpiresByType text/xml "access plus 1 day"
    
    # Manifest files: 1 week
    ExpiresByType application/manifest+json "access plus 1 week"
    ExpiresByType text/cache-manifest "access plus 1 week"
</IfModule>

# Cache Control with mod_headers
<IfModule mod_headers.c>
    # Remove ETags (we use Cache-Control instead)
    Header unset ETag
    FileETag None
    
    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Service Worker
    <FilesMatch "sw\.js$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Service-Worker-Allowed "/"
    </FilesMatch>
    
    # Hashed static assets (immutable)
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|webp|avif|woff|woff2|ttf|eot)$">
        <If "%{REQUEST_URI} =~ /-[a-f0-9]{8,}\.(css|js|png|jpg|jpeg|gif|svg|webp|avif|woff|woff2|ttf|eot)$/">
            Header set Cache-Control "public, max-age=31536000, immutable"
        </If>
        <Else>
            Header set Cache-Control "public, max-age=31536000"
        </Else>
    </FilesMatch>
    
    # HTML files
    <FilesMatch "\.html$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>
    
    # JSON files
    <FilesMatch "\.json$">
        Header set Cache-Control "public, max-age=86400"
    </FilesMatch>
    
    # Manifest files
    <FilesMatch "\.(webmanifest|manifest)$">
        Header set Cache-Control "public, max-age=604800"
    </FilesMatch>
    
    # API responses (if served statically)
    <LocationMatch "^/api/">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </LocationMatch>
</IfModule>

# MIME Types
<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff2 .woff2
    AddType application/font-woff .woff
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType font/opentype .otf
    
    # Modern image formats
    AddType image/webp .webp
    AddType image/avif .avif
    
    # Manifest
    AddType application/manifest+json .webmanifest
    AddType application/manifest+json .manifest
    
    # Service Worker
    AddType application/javascript .js
</IfModule>

# Prevent access to sensitive files
<FilesMatch "\.(env|log|sql|md|txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Enable CORS for fonts and assets (if needed)
<IfModule mod_headers.c>
    <FilesMatch "\.(woff|woff2|ttf|eot|otf)$">
        Header set Access-Control-Allow-Origin "*"
    </FilesMatch>
</IfModule>

# Redirect HTTP to HTTPS (uncomment if using HTTPS)
# <IfModule mod_rewrite.c>
#     RewriteEngine On
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>

# Performance optimizations
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Remove trailing slash from non-directory URLs
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]
    
    # Handle client-side routing (SPA)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/api/
    RewriteRule . /index.html [L]
</IfModule>

# Optimize file delivery
<IfModule mod_headers.c>
    # Preload critical resources
    <FilesMatch "index\.html$">
        Header add Link "</optimized/favicon-32x32.webp>; rel=preload; as=image; type=image/webp"
        Header add Link "</optimized/favicon-32x32.avif>; rel=preload; as=image; type=image/avif"
    </FilesMatch>
</IfModule>
