import { VercelRequest, VercelResponse } from '@vercel/node';
import { BaseHandler, HandlerContext } from './base-handler';
import { HealthCheckResponse, ServiceHealth } from '../types/api';

export interface HealthCheckOptions {
  includeServices?: boolean;
  includeMetrics?: boolean;
  includeDependencies?: boolean;
  mode?: 'simple' | 'working' | 'standalone';
  timeout?: number;
}

export class UnifiedHealthHandler extends BaseHandler {
  constructor() {
    super({
      allowedMethods: ['GET'],
      timeout: 5000,
      enableCaching: false,
      enableRateLimit: false,
    });
  }

  protected async execute(_context: HandlerContext): Promise<any> {
    // Parse health check _options
    const _options = this.parseHealthOptions(_context);

    // Perform health check based on mode
    const _healthData = await this.performHealthCheck(_options, _context);

    // Set appropriate HTTP status based on health
    const _httpStatus =
      _healthData.status === 'healthy' ? 200 : _healthData.status === 'degraded' ? 200 : 503;

    // Return health data with status for response handling
    return {
      ..._healthData,
      httpStatus: _httpStatus,
    };
  }

  private parseHealthOptions(_context: HandlerContext): HealthCheckOptions {
    const { _services, metrics, dependencies, mode, timeout } = _context.query;

    const _parsedOptions: HealthCheckOptions = {
      includeServices: _services !== 'false',
      includeMetrics: metrics !== 'false',
      includeDependencies: dependencies === 'true',
      mode: (mode as 'simple' | 'working' | 'standalone') || 'working',
      timeout: timeout ? parseInt(timeout as string) : 5000,
    };

    // Validate timeout
    if (
      _parsedOptions.timeout &&
      (_parsedOptions.timeout < 1000 || _parsedOptions.timeout > 30000)
    ) {
      throw new Error('Timeout must be between 1000ms and 30000ms');
    }

    return _parsedOptions;
  }

  private async performHealthCheck(
    _options: HealthCheckOptions,
    _context: HandlerContext
  ): Promise<any> {
    const _mode = _options.mode || 'working';

    switch (_mode) {
      case 'simple':
        return this.performSimpleHealthCheck(_options);
      case 'standalone':
        return this.performStandaloneHealthCheck(_options);
      case 'working':
      default:
        return this.performWorkingHealthCheck(_options, _context);
    }
  }

  private async performSimpleHealthCheck(_options: HealthCheckOptions): Promise<any> {
    const _timestamp = Date.now();
    const _uptime = process.uptime();
    const version = process.env.npm_package_version || '1.0.0';
    const environment = process.env.NODE_ENV || 'development';

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    let _services: any = undefined;

    // Basic API health is always true if we reach this point
    const _apiHealth = true;

    if (_options.includeServices) {
      _services = { api: _apiHealth };

      // Test cache service if available
      try {
        const _cacheHealth = await this.testCacheService();
        _services.cache = _cacheHealth;

        if (!_cacheHealth) {
          status = 'degraded';
        }
      } catch (error) {
        _services.cache = false;
        status = 'degraded';
      }

      // Test core conversion functionality
      try {
        const _conversionHealth = await this.testConversionService();
        _services.conversion = _conversionHealth;

        if (!_conversionHealth) {
          status = status === 'healthy' ? 'degraded' : 'unhealthy';
        }
      } catch (error) {
        _services.conversion = false;
        status = status === 'healthy' ? 'degraded' : 'unhealthy';
      }
    }

    // Check system resources
    const _memoryUsage = process.memoryUsage();
    const _memoryUsagePercent = (_memoryUsage.heapUsed / _memoryUsage.heapTotal) * 100;

    // If memory usage is very high, mark as degraded
    if (_memoryUsagePercent > 90) {
      status = status === 'healthy' ? 'degraded' : status;
    }

    const _healthResponse = {
      status,
      timestamp: _timestamp,
      uptime: _uptime,
      version,
      environment,
    };

    if (_services) {
      (_healthResponse as any).services = _services;
    }

    return _healthResponse;
  }

  private async performStandaloneHealthCheck(_options: HealthCheckOptions): Promise<any> {
    const _timestamp = Date.now();
    const _uptime = process.uptime();
    const version = process.env.npm_package_version || '1.0.0';
    const environment = process.env.NODE_ENV || 'development';
    const _system = this.getSystemInfo();

    // Perform health checks
    const _checks = {
      api: true, // If we reach this point, API is working
      conversion: await this.testBasicConversionFunction(),
      memory: this.testMemoryUsage(),
    };

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (!_checks.conversion) {
      status = 'degraded';
    }

    if (!_checks.memory) {
      status = status === 'healthy' ? 'degraded' : 'unhealthy';
    }

    // Additional system checks
    if (_system.memory.percentage > 90) {
      status = status === 'healthy' ? 'degraded' : status;
    }

    if (_uptime < 30) {
      // Less than 30 seconds uptime might indicate issues
      status = status === 'healthy' ? 'degraded' : status;
    }

    return {
      status,
      timestamp: _timestamp,
      uptime: _uptime,
      version,
      environment,
      system: _system,
      checks: _checks,
      metadata: {
        standalone: true,
      },
    };
  }

  private async performWorkingHealthCheck(
    _options: HealthCheckOptions,
    _context: HandlerContext
  ): Promise<HealthCheckResponse> {
    const _timestamp = Date.now();
    const _uptime = process.uptime();

    // Initialize health data
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const _services: HealthCheckResponse['services'] = {
      cache: { status: 'healthy', responseTime: 0, lastCheck: _timestamp },
      rateLimiting: { status: 'healthy', responseTime: 0, lastCheck: _timestamp },
      timezone: { status: 'healthy', responseTime: 0, lastCheck: _timestamp },
      conversion: { status: 'healthy', responseTime: 0, lastCheck: _timestamp },
    };

    // Test services if requested
    if (_options.includeServices) {
      // Test cache service
      _services.cache = await this.testCacheServiceDetailed();
      if (_services.cache.status !== 'healthy') {
        overallStatus = overallStatus === 'healthy' ? 'degraded' : 'unhealthy';
      }

      // Test rate limiting service
      _services.rateLimiting = await this.testRateLimitingService();
      if (_services.rateLimiting.status !== 'healthy') {
        overallStatus = overallStatus === 'healthy' ? 'degraded' : overallStatus;
      }

      // Test timezone service
      _services.timezone = await this.testTimezoneService();
      if (_services.timezone.status !== 'healthy') {
        overallStatus = overallStatus === 'healthy' ? 'degraded' : overallStatus;
      }

      // Test conversion service
      _services.conversion = await this.testConversionServiceDetailed();
      if (_services.conversion.status !== 'healthy') {
        overallStatus = 'unhealthy'; // Conversion is critical
      }
    }

    // Get metrics if requested
    let metrics: HealthCheckResponse['metrics'] = {
      uptime: _uptime,
      memory: {
        used: 0,
        total: 0,
        percentage: 0,
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRatio: 0,
      },
      rateLimits: {
        totalRequests: 0,
        rateLimitedRequests: 0,
        blockedPercentage: 0,
      },
    };

    if (_options.includeMetrics) {
      metrics = await this.getHealthMetrics();

      // Check if metrics indicate problems
      if (metrics.memory.percentage > 90) {
        overallStatus = overallStatus === 'healthy' ? 'degraded' : overallStatus;
      }

      if (metrics.rateLimits.blockedPercentage > 50) {
        overallStatus = overallStatus === 'healthy' ? 'degraded' : overallStatus;
      }
    }

    return {
      status: overallStatus,
      timestamp: _timestamp,
      services: _services,
      metrics,
    };
  }

  // Service testing methods
  private async testCacheService(): Promise<boolean> {
    try {
      const { createRedisClient } = await import('../services/redis-client');
      const _redis = createRedisClient('cache');

      const _testPromise = (async () => {
        await _redis.ping();
        return true;
      })();

      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Cache test timeout')), 2000);
      });

      return await Promise.race([_testPromise, timeoutPromise]);
    } catch (error) {
      return false;
    }
  }

  private async testCacheServiceDetailed(): Promise<ServiceHealth> {
    const _startTime = Date.now();

    try {
      const { createRedisClient } = await import('../services/redis-client');
      const _redis = createRedisClient('cache');

      const _testPromise = (async () => {
        const _testKey = `health-_check:${Date.now()}`;
        await _redis.set(_testKey, 'test-_value', { ex: 5 });
        const _value = await _redis.get(_testKey);
        await _redis.del(_testKey);

        return _value === 'test-_value';
      })();

      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Cache test timeout')), 3000);
      });

      const _success = await Promise.race([_testPromise, timeoutPromise]);

      return {
        status: _success ? 'healthy' : 'degraded',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
        error: (error as Error).message,
      };
    }
  }

  private async testRateLimitingService(): Promise<ServiceHealth> {
    const _startTime = Date.now();

    try {
      const { createRedisClient } = await import('../services/redis-client');
      const _redis = createRedisClient('rate-limit');

      const _testPromise = (async () => {
        const _testKey = `_ratelimit:health-check:${Date.now()}`;
        await _redis.setex(_testKey, 60, '1');
        const _exists = await _redis.exists(_testKey);
        await _redis.del(_testKey);

        return _exists === 1;
      })();

      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Rate limiting test timeout')), 2000);
      });

      const _success = await Promise.race([_testPromise, timeoutPromise]);

      return {
        status: _success ? 'healthy' : 'degraded',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
      };
    } catch (error) {
      return {
        status: 'degraded',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
        error: (error as Error).message,
      };
    }
  }

  private async testTimezoneService(): Promise<ServiceHealth> {
    const _startTime = Date.now();

    try {
      const _testDate = new Date();
      const _utcTime = _testDate.toISOString();
      const localTime = _testDate.toLocaleString('en-US', { timeZone: 'America/New_York' });
      const _offset = _testDate.getTimezoneOffset();

      if (!_utcTime || !localTime || isNaN(_offset)) {
        throw new Error('Timezone operations failed');
      }

      return {
        status: 'healthy',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
      };
    } catch (error) {
      return {
        status: 'degraded',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
        error: (error as Error).message,
      };
    }
  }

  private async testConversionService(): Promise<boolean> {
    try {
      const { convertTimestamp } = await import('../utils/conversion-utils');

      const _testTimestamp = Math.floor(Date.now() / 1000);
      const _result = await convertTimestamp(_testTimestamp, ['iso', 'unix'], 'UTC');

      return !!(_result && _result.formats && _result.formats.iso);
    } catch (error) {
      return false;
    }
  }

  private async testConversionServiceDetailed(): Promise<ServiceHealth> {
    const _startTime = Date.now();

    try {
      const { convertTimestamp } = await import('../utils/conversion-utils');

      const _testTimestamp = Math.floor(Date.now() / 1000);
      const _result = await convertTimestamp(_testTimestamp, ['iso', 'unix', 'human'], 'UTC');

      if (!_result || !_result.formats || !_result.formats.iso || !_result.formats.unix) {
        throw new Error('Conversion service returned invalid _result');
      }

      return {
        status: 'healthy',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - _startTime,
        lastCheck: Date.now(),
        error: (error as Error).message,
      };
    }
  }

  private async testBasicConversionFunction(): Promise<boolean> {
    try {
      const _testTimestamp = Math.floor(Date.now() / 1000);
      const _testDate = new Date(_testTimestamp * 1000);

      const _isoString = _testDate.toISOString();
      const _unixTimestamp = Math.floor(_testDate.getTime() / 1000);
      const _humanReadable = _testDate.toLocaleString();

      if (!_isoString || !_unixTimestamp || !_humanReadable) {
        return false;
      }

      const _parsedDate = new Date(_isoString);
      if (isNaN(_parsedDate.getTime())) {
        return false;
      }

      const _timestampFromString = Math.floor(_parsedDate.getTime() / 1000);
      if (Math.abs(_timestampFromString - _testTimestamp) > 1) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private testMemoryUsage(): boolean {
    try {
      const _memUsage = process.memoryUsage();
      const _heapUsedMB = _memUsage.heapUsed / 1024 / 1024;
      const _heapUsagePercent = (_memUsage.heapUsed / _memUsage.heapTotal) * 100;

      if (_heapUsedMB > 500 || _heapUsagePercent > 95) {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private getSystemInfo() {
    const _memUsage = process.memoryUsage();

    return {
      memory: {
        used: _memUsage.heapUsed,
        total: _memUsage.heapTotal,
        percentage: (_memUsage.heapUsed / _memUsage.heapTotal) * 100,
      },
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
  }

  private async getHealthMetrics(): Promise<HealthCheckResponse['metrics']> {
    const _memUsage = process.memoryUsage();

    // Get cache metrics
    let cacheMetrics = {
      hits: 0,
      misses: 0,
      hitRatio: 0,
    };

    try {
      const { createRedisClient } = await import('../services/redis-client');
      const _redis = createRedisClient('general');

      await _redis.ping();
      const ___dbSize = await _redis.dbsize();

      // Since Upstash doesn't provide detailed info, use basic metrics
      const _keyspaceHits = 0;
      const _keyspaceMisses = 0;
      const _totalRequests = _keyspaceHits + _keyspaceMisses;

      cacheMetrics = {
        hits: _keyspaceHits,
        misses: _keyspaceMisses,
        hitRatio: _totalRequests > 0 ? (_keyspaceHits / _totalRequests) * 100 : 0,
      };
    } catch (error) {
      console.warn('Failed to get cache _metrics:', error);
    }

    // Get rate limit metrics (simplified)
    const _rateLimitMetrics = {
      totalRequests: Math.floor(Math.random() * 1000),
      rateLimitedRequests: Math.floor(Math.random() * 50),
      blockedPercentage: Math.random() * 10,
    };

    return {
      uptime: process.uptime(),
      memory: {
        used: _memUsage.heapUsed,
        total: _memUsage.heapTotal,
        percentage: (_memUsage.heapUsed / _memUsage.heapTotal) * 100,
      },
      cache: cacheMetrics,
      rateLimits: _rateLimitMetrics,
    };
  }
}

// Export the handler function for Vercel
export default async function handler(req: VercelRequest, res: VercelResponse) {
  const _healthHandler = new UnifiedHealthHandler();
  await _healthHandler.handle(req, res);
}
