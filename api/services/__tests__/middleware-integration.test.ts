import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { VercelRequest, VercelResponse } from '@vercel/node';
import cacheService from '../cache-service';
import { rateLimiter } from '../rate-limiter';
import { <PERSON>rror<PERSON>and<PERSON> } from '../../middleware/error-handler';
import { createCacheMiddleware } from '../../middleware/cache';
import { createRateLimitMiddleware } from '../../middleware/rate-limit';

// Create middleware instances for testing
const cacheMiddlewareFactory = createCacheMiddleware();
const rateLimitMiddlewareFactory = createRateLimitMiddleware();

// Create wrapper functions that match the expected signature
const cacheMiddleware = async (req: VercelRequest, res: VercelResponse, next: Function) => {
  try {
    const handler = (req: VercelRequest, res: VercelResponse) => {
      next();
      return Promise.resolve();
    };

    const wrappedHandler = cacheMiddlewareFactory(handler);
    return wrappedHandler(req, res);
  } catch (error) {
    console.warn('Cache middleware error:', error);
    next();
  }
};

const rateLimitMiddleware = async (req: VercelRequest, res: VercelResponse, next: Function) => {
  try {
    const handler = (req: VercelRequest, res: VercelResponse) => {
      next();
      return Promise.resolve();
    };

    const wrappedHandler = rateLimitMiddlewareFactory(handler);
    return wrappedHandler(req, res);
  } catch (error) {
    console.warn('Rate limit middleware error:', error);
    next();
  }
};

describe('Middleware Integration Tests', () => {
  let mockReq: Partial<VercelRequest>;
  let mockRes: Partial<VercelResponse>;
  let nextFunction: vi.Mock;

  beforeEach(async () => {
    // Clear cache before each test
    await cacheService.clear();

    // Enable rate limiting for tests by overriding config
    const config = await import('../../config/config');
    config.default.rateLimiting.enabled = true;

    mockReq = {
      query: {},
      body: {},
      method: 'GET',
      url: '/api/test',
      headers: {
        'x-forwarded-for': '*************',
      },
    };

    const statusMock = vi.fn().mockReturnThis();
    const jsonMock = vi.fn().mockReturnThis();
    const setHeaderMock = vi.fn().mockReturnThis();
    const endMock = vi.fn().mockReturnThis();
    const getHeaderMock = vi.fn();

    mockRes = {
      status: statusMock,
      json: jsonMock,
      setHeader: setHeaderMock,
      end: endMock,
      getHeader: getHeaderMock,
      locals: {},
    };

    nextFunction = vi.fn();

    // Reset rate limiter state
    await rateLimiter.reset('ip:*************', {
      identifier: 'anonymous',
      limit: 100,
      window: 60000,
      type: 'ip',
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Cache + Rate Limit Integration', () => {
    it('should apply rate limiting before cache check', async () => {
      const testKey = 'test-cache-key';
      const testData = { result: 'cached-data' };

      // Pre-populate cache
      await cacheService.set(testKey, testData, 60000);

      // Set up request that would hit cache
      mockReq.query = { timestamp: '1640995200' };

      // Apply rate limiting first
      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Should not be rate limited initially
      expect(nextFunction).toHaveBeenCalled();

      // Apply cache middleware
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Should proceed to next middleware
      expect(nextFunction).toHaveBeenCalledTimes(2);
    });

    it('should prevent cache access when rate limited', async () => {
      // Create a fresh mock response for each request to avoid interference
      const testRes = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis(),
        getHeader: vi.fn(),
        locals: {},
      };

      // Exhaust rate limit with a specific IP
      const testIp = '*************';
      const requests = Array.from({ length: 105 }, (_, i) => ({
        ...mockReq,
        headers: { 'x-forwarded-for': testIp },
      }));

      // Make requests to exhaust rate limit
      for (let i = 0; i < 105; i++) {
        const freshRes = {
          status: vi.fn().mockReturnThis(),
          json: vi.fn().mockReturnThis(),
          setHeader: vi.fn().mockReturnThis(),
          end: vi.fn().mockReturnThis(),
          getHeader: vi.fn(),
          locals: {},
        };
        await rateLimitMiddleware(
          requests[i] as VercelRequest,
          freshRes as VercelResponse,
          nextFunction
        );
      }

      // Next request should be rate limited
      const rateLimitedReq = {
        ...mockReq,
        headers: { 'x-forwarded-for': testIp },
      };

      await rateLimitMiddleware(
        rateLimitedReq as VercelRequest,
        testRes as VercelResponse,
        nextFunction
      );

      // Should be rate limited and not proceed to cache
      expect(testRes.status).toHaveBeenCalledWith(429);
    });

    it('should cache rate limit information', async () => {
      mockReq.query = { timestamp: '1640995200' };

      // Create rate limit middleware with legacy headers enabled
      const rateLimitMiddlewareWithHeaders = createRateLimitMiddleware({
        legacyHeaders: true,
        standardHeaders: true,
      });

      const rateLimitMiddlewareTest = async (
        req: VercelRequest,
        res: VercelResponse,
        next: Function
      ) => {
        const handler = (req: VercelRequest, res: VercelResponse) => {
          next();
          return Promise.resolve();
        };

        const wrappedHandler = rateLimitMiddlewareWithHeaders(handler);
        return wrappedHandler(req, res);
      };

      // Apply rate limiting
      await rateLimitMiddlewareTest(
        mockReq as VercelRequest,
        mockRes as VercelResponse,
        nextFunction
      );

      // Check that rate limit headers are set
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Limit', expect.any(Number));
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', expect.any(Number));
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Reset', expect.any(Number));
    });

    it('should respect cache-control headers', async () => {
      // Set up request with cache-control header
      mockReq.headers = {
        ...mockReq.headers,
        'cache-control': 'no-cache',
      };

      mockReq.query = { timestamp: '1640995200' };

      // Create cache middleware with skip function for no-cache
      const cacheMiddlewareWithSkip = createCacheMiddleware({
        skipCache: req => req.headers['cache-control'] === 'no-cache',
      });

      const cacheMiddlewareTest = async (
        req: VercelRequest,
        res: VercelResponse,
        next: Function
      ) => {
        const handler = (req: VercelRequest, res: VercelResponse) => {
          next();
          return Promise.resolve();
        };

        const wrappedHandler = cacheMiddlewareWithSkip(handler);
        return wrappedHandler(req, res);
      };

      // Pre-populate cache
      const cacheKey = 'cache:test:/api/test:anonymous:timestamp:1640995200';
      await cacheService.set(cacheKey, { data: 'cached-result' }, 60000);

      // Apply cache middleware
      await cacheMiddlewareTest(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Should bypass cache due to no-cache header
      expect(nextFunction).toHaveBeenCalled();
    });
  });

  describe('Cache + Error Handler Integration', () => {
    it('should handle cache errors gracefully', async () => {
      // Mock cache service to throw error
      const originalGet = cacheService.get;
      cacheService.get = vi.fn().mockRejectedValue(new Error('Cache service unavailable'));

      try {
        mockReq.query = { timestamp: '1640995200' };

        await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

        // Should continue to next middleware despite cache error
        expect(nextFunction).toHaveBeenCalled();

        // Should log the error but not fail the request
        expect(mockRes.status).not.toHaveBeenCalledWith(500);
      } finally {
        // Restore original method
        cacheService.get = originalGet;
      }
    });

    it('should provide error context for cache failures', async () => {
      const errorHandler = ErrorHandler.getInstance();

      // Mock cache service to throw error
      const originalSet = cacheService.set;
      cacheService.set = vi.fn().mockRejectedValue(new Error('Cache write failed'));

      try {
        mockReq.query = { timestamp: '1640995200' };

        // Simulate a response that would be cached
        mockRes.locals = { cacheKey: 'test-key', cacheData: { result: 'test' } };

        await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

        // Should handle cache write failure gracefully
        expect(nextFunction).toHaveBeenCalled();

        // Error should be logged but not propagated
        const errorSummary = errorHandler.getErrorSummary();
        expect(errorSummary.total).toBeGreaterThanOrEqual(0);
      } finally {
        // Restore original method
        cacheService.set = originalSet;
      }
    });

    it('should handle errors in error handler gracefully', async () => {
      // Create a situation where error handler itself might fail
      const errorHandler = ErrorHandler.getInstance();
      const originalLogError = errorHandler.logError;

      // Make the error handler's logError method throw
      errorHandler.logError = vi.fn().mockImplementation(() => {
        throw new Error('Error handler failure');
      });

      try {
        // Mock cache service to throw error
        const originalGet = cacheService.get;
        cacheService.get = vi.fn().mockRejectedValue(new Error('Cache service unavailable'));

        try {
          mockReq.query = { timestamp: '1640995200' };

          // This should trigger the cache error, which then triggers error handler
          await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

          // Should still continue to next middleware despite nested errors
          expect(nextFunction).toHaveBeenCalled();

          // Should not crash the application
          expect(mockRes.status).not.toHaveBeenCalledWith(500);
        } finally {
          // Restore original method
          cacheService.get = originalGet;
        }
      } finally {
        // Restore original error handler method
        errorHandler.logError = originalLogError;
      }
    });
  });

  describe('Rate Limit + Error Handler Integration', () => {
    it('should handle rate limiter errors gracefully', async () => {
      // Mock rate limiter to throw error
      const originalIncrement = rateLimiter.increment;
      rateLimiter.increment = vi.fn().mockRejectedValue(new Error('Rate limiter unavailable'));

      try {
        mockReq.query = { timestamp: '1640995200' };

        await rateLimitMiddleware(
          mockReq as VercelRequest,
          mockRes as VercelResponse,
          nextFunction
        );

        // Should continue despite rate limiter error (fail open)
        expect(nextFunction).toHaveBeenCalled();
        expect(mockRes.status).not.toHaveBeenCalledWith(500);
      } finally {
        // Restore original method
        rateLimiter.increment = originalIncrement;
      }
    });

    it('should provide detailed error information for rate limit failures', async () => {
      const testIp = '*************';
      const testReq = {
        ...mockReq,
        headers: { 'x-forwarded-for': testIp },
      };

      // Exhaust rate limit
      for (let i = 0; i < 105; i++) {
        const freshRes = {
          status: vi.fn().mockReturnThis(),
          json: vi.fn().mockReturnThis(),
          setHeader: vi.fn().mockReturnThis(),
          end: vi.fn().mockReturnThis(),
          getHeader: vi.fn(),
          locals: {},
        };
        await rateLimitMiddleware(
          testReq as VercelRequest,
          freshRes as VercelResponse,
          nextFunction
        );
      }

      // Create fresh response for final test
      const finalRes = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis(),
        getHeader: vi.fn(),
        locals: {},
      };

      // Next request should be rate limited with detailed error
      await rateLimitMiddleware(testReq as VercelRequest, finalRes as VercelResponse, nextFunction);

      expect(finalRes.status).toHaveBeenCalledWith(429);
      expect(finalRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.objectContaining({
            code: 'RATE_LIMIT_EXCEEDED',
            message: expect.stringContaining('Rate limit exceeded'),
            suggestions: expect.arrayContaining([expect.any(String)]),
          }),
        })
      );
    });

    it('should include retry-after header when rate limited', async () => {
      const testIp = '*************';
      const testReq = {
        ...mockReq,
        headers: { 'x-forwarded-for': testIp },
      };

      // Exhaust rate limit
      for (let i = 0; i < 105; i++) {
        const freshRes = {
          status: vi.fn().mockReturnThis(),
          json: vi.fn().mockReturnThis(),
          setHeader: vi.fn().mockReturnThis(),
          end: vi.fn().mockReturnThis(),
          getHeader: vi.fn(),
          locals: {},
        };
        await rateLimitMiddleware(
          testReq as VercelRequest,
          freshRes as VercelResponse,
          nextFunction
        );
      }

      // Create fresh response for final test
      const finalRes = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn().mockReturnThis(),
        setHeader: vi.fn().mockReturnThis(),
        end: vi.fn().mockReturnThis(),
        getHeader: vi.fn(),
        locals: {},
      };

      // Next request should be rate limited
      await rateLimitMiddleware(testReq as VercelRequest, finalRes as VercelResponse, nextFunction);

      // Should include retry-after header
      expect(finalRes.setHeader).toHaveBeenCalledWith('Retry-After', expect.any(Number));
    });
  });

  describe('Full Middleware Stack Integration', () => {
    it('should process requests through complete middleware stack', async () => {
      mockReq.query = { timestamp: '1640995200' };

      // Create middleware with headers enabled
      const rateLimitMiddlewareWithHeaders = createRateLimitMiddleware({
        legacyHeaders: true,
      });

      const rateLimitTest = async (req: VercelRequest, res: VercelResponse, next: Function) => {
        const handler = (req: VercelRequest, res: VercelResponse) => {
          next();
          return Promise.resolve();
        };

        const wrappedHandler = rateLimitMiddlewareWithHeaders(handler);
        return wrappedHandler(req, res);
      };

      // Simulate full middleware stack: rate limit -> cache -> error handler
      await rateLimitTest(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);

      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(2);

      // Should have rate limit headers
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Limit', expect.any(Number));

      // Should not have been blocked - middleware should continue to next
      // (We can't easily test status calls since middleware doesn't call them in success cases)
    });

    it('should handle middleware failures in sequence', async () => {
      // Mock rate limiter to fail
      const originalCheckLimit = rateLimiter.checkLimit;
      rateLimiter.checkLimit = vi.fn().mockRejectedValue(new Error('Rate limiter down'));

      // Mock cache to fail
      const originalGet = cacheService.get;
      cacheService.get = vi.fn().mockRejectedValue(new Error('Cache down'));

      try {
        mockReq.query = { timestamp: '1640995200' };

        // Should handle both failures gracefully
        await rateLimitMiddleware(
          mockReq as VercelRequest,
          mockRes as VercelResponse,
          nextFunction
        );
        await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

        // Should continue processing despite failures
        expect(nextFunction).toHaveBeenCalledTimes(2);
      } finally {
        // Restore original methods
        rateLimiter.checkLimit = originalCheckLimit;
        cacheService.get = originalGet;
      }
    });

    it('should maintain request context through middleware stack', async () => {
      mockReq.query = { timestamp: '1640995200', format: 'iso8601' };

      // Add request ID for tracking
      const requestId = 'test-request-123';
      mockReq.headers = {
        ...mockReq.headers,
        'x-request-id': requestId,
      };

      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Request context should be preserved
      expect(mockReq.headers['x-request-id']).toBe(requestId);
      expect(nextFunction).toHaveBeenCalledTimes(2);
    });

    it('should handle different HTTP methods correctly', async () => {
      // Test GET request
      mockReq.method = 'GET';
      mockReq.query = { timestamp: '1640995200' };

      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(2);

      // Reset mocks
      nextFunction.mockClear();

      // Test POST request
      mockReq.method = 'POST';
      mockReq.body = { timestamp: '1640995200' };
      mockReq.query = {};

      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      expect(nextFunction).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance Under Load', () => {
    it('should handle concurrent requests efficiently', async () => {
      const concurrentRequests = 50;
      const requests = Array.from({ length: concurrentRequests }, (_, i) => ({
        ...mockReq,
        query: { timestamp: String(1640995200 + i) },
        headers: { 'x-forwarded-for': `192.168.1.${100 + (i % 10)}` },
      }));

      const start = performance.now();

      // Process all requests concurrently
      const promises = requests.map(async (req, index) => {
        const res = {
          ...mockRes,
          status: vi.fn().mockReturnThis(),
          json: vi.fn().mockReturnThis(),
          setHeader: vi.fn().mockReturnThis(),
        };
        const next = vi.fn();

        await rateLimitMiddleware(req as VercelRequest, res as VercelResponse, next);
        await cacheMiddleware(req as VercelRequest, res as VercelResponse, next);

        return { req, res, next };
      });

      const results = await Promise.all(promises);
      const duration = performance.now() - start;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(2000); // 2 seconds for 50 requests

      // Most requests should succeed (some might be rate limited)
      const successfulRequests = results.filter(
        r => !r.res.status.mock?.calls?.some(call => call[0] === 429)
      );
      expect(successfulRequests.length).toBeGreaterThan(concurrentRequests * 0.6); // Lower threshold due to rate limiting
    });

    it('should maintain cache performance under load', async () => {
      // Pre-populate cache with test data
      const cachePromises = Array.from({ length: 100 }, (_, i) =>
        cacheService.set(`load-test-${i}`, { data: `value-${i}` }, 60000)
      );
      await Promise.all(cachePromises);

      const start = performance.now();

      // Perform many cache operations
      const operations = Array.from({ length: 200 }, async (_, i) => {
        if (i % 2 === 0) {
          return cacheService.get(`load-test-${i % 100}`);
        } else {
          return cacheService.set(`load-test-new-${i}`, { data: `new-${i}` }, 60000);
        }
      });

      await Promise.all(operations);
      const duration = performance.now() - start;

      // Should complete cache operations efficiently
      expect(duration).toBeLessThan(1000); // 1 second for 200 operations

      // Verify cache stats
      const stats = await cacheService.stats();
      expect(stats.size).toBeGreaterThan(100);
      expect(stats.hits).toBeGreaterThan(0);
    });

    it('should handle high rate limit throughput', async () => {
      // Create many different IP addresses
      const ipCount = 50;
      const requestsPerIp = 10;

      const start = performance.now();

      // Process requests from different IPs
      const promises = Array.from({ length: ipCount }, async (_, i) => {
        const ipRequests = Array.from({ length: requestsPerIp }, async (_, j) => {
          const req = {
            ...mockReq,
            headers: { 'x-forwarded-for': `192.168.1.${i + 1}` },
            query: { timestamp: String(1640995200 + j) },
          };

          const res = {
            ...mockRes,
            status: vi.fn().mockReturnThis(),
            json: vi.fn().mockReturnThis(),
            setHeader: vi.fn().mockReturnThis(),
          };

          const next = vi.fn();

          await rateLimitMiddleware(req as VercelRequest, res as VercelResponse, next);
          return { req, res, next };
        });

        return Promise.all(ipRequests);
      });

      await Promise.all(promises);
      const duration = performance.now() - start;

      // Should handle high throughput efficiently
      expect(duration).toBeLessThan(5000); // 5 seconds for 500 requests
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from transient failures', async () => {
      let failureCount = 0;
      const maxFailures = 3;

      // Mock intermittent cache failures
      const originalGet = cacheService.get;
      cacheService.get = vi.fn().mockImplementation(async (key: string) => {
        if (failureCount < maxFailures) {
          failureCount++;
          throw new Error('Transient failure');
        }
        return originalGet.call(cacheService, key);
      });

      try {
        // Make requests that should eventually succeed
        for (let i = 0; i < 5; i++) {
          const req = { ...mockReq, query: { timestamp: String(1640995200 + i) } };
          await cacheMiddleware(req as VercelRequest, mockRes as VercelResponse, nextFunction);
        }

        // Should continue processing despite initial failures
        expect(nextFunction).toHaveBeenCalledTimes(5);
        expect(failureCount).toBe(maxFailures);
      } finally {
        cacheService.get = originalGet;
      }
    });

    it('should handle concurrent access to the same key', async () => {
      const key = 'concurrent-key';
      const operations = [];

      // Create 50 concurrent operations on the same key
      for (let i = 0; i < 25; i++) {
        operations.push(cacheService.set(key, { value: i }));
        operations.push(cacheService.get(key));
      }

      // Should not throw errors
      await expect(Promise.all(operations)).resolves.toBeDefined();

      // Key should exist after all operations
      const finalResult = await cacheService.get<{ value: number }>(key);
      expect(finalResult).toBeDefined();
    });

    it('should handle circuit breaker behavior for repeated failures', async () => {
      let callCount = 0;

      // Mock consistent cache failures
      const originalGet = cacheService.get;
      cacheService.get = vi.fn().mockImplementation(async () => {
        callCount++;
        throw new Error('Persistent failure');
      });

      try {
        // Make multiple requests
        for (let i = 0; i < 10; i++) {
          const req = { ...mockReq, query: { timestamp: String(1640995200 + i) } };
          await cacheMiddleware(req as VercelRequest, mockRes as VercelResponse, nextFunction);
        }

        // Should continue processing (fail open)
        expect(nextFunction).toHaveBeenCalledTimes(10);

        // Cache should have been called for each request (no circuit breaker in current implementation)
        expect(callCount).toBe(10);
      } finally {
        cacheService.get = originalGet;
      }
    });
  });

  describe('Security and Validation', () => {
    it('should validate request parameters before processing', async () => {
      // Test with malicious input
      mockReq.query = {
        timestamp: '<script>alert("xss")</script>',
        format: '../../etc/passwd',
      };

      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Should continue processing (validation happens in handlers)
      expect(nextFunction).toHaveBeenCalledTimes(2);
    });

    it('should handle oversized requests appropriately', async () => {
      // Create large request body
      const largeData = {
        items: Array.from({ length: 10000 }, (_, i) => `item-${i}`),
      };

      mockReq.body = largeData;
      mockReq.method = 'POST';

      await rateLimitMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);
      await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

      // Should handle large requests (size limits enforced at API level)
      expect(nextFunction).toHaveBeenCalledTimes(2);
    });

    it('should sanitize error messages', async () => {
      // Mock cache service to throw error with potentially sensitive info
      const originalGet = cacheService.get;
      cacheService.get = vi
        .fn()
        .mockRejectedValue(new Error('Database password is invalid: abc123'));

      try {
        mockReq.query = { timestamp: '1640995200' };

        // This should trigger the cache error
        await cacheMiddleware(mockReq as VercelRequest, mockRes as VercelResponse, nextFunction);

        // Should continue to next middleware
        expect(nextFunction).toHaveBeenCalled();

        // If error is exposed, it should be sanitized
        if (mockRes.json.mock.calls.length > 0) {
          const response = mockRes.json.mock.calls[0][0];
          if (response.error) {
            // Error message should not contain the sensitive info
            expect(response.error.message).not.toContain('abc123');
          }
        }
      } finally {
        // Restore original method
        cacheService.get = originalGet;
      }
    });
  });

  describe('Monitoring and Observability', () => {
    it('should track middleware performance metrics', async () => {
      const requests = Array.from({ length: 10 }, (_, i) => ({
        ...mockReq,
        query: { timestamp: String(1640995200 + i) },
      }));

      const timings: number[] = [];

      for (const req of requests) {
        const start = performance.now();

        await rateLimitMiddleware(req as VercelRequest, mockRes as VercelResponse, nextFunction);
        await cacheMiddleware(req as VercelRequest, mockRes as VercelResponse, nextFunction);

        timings.push(performance.now() - start);
      }

      // Calculate average response time
      const avgTime = timings.reduce((sum, time) => sum + time, 0) / timings.length;

      // Should be reasonably fast
      expect(avgTime).toBeLessThan(50); // 50ms average

      // Should have consistent performance
      const maxTime = Math.max(...timings);
      const minTime = Math.min(...timings);
      expect(maxTime - minTime).toBeLessThan(100); // Within 100ms variance
    });

    it('should provide health check information for middleware', async () => {
      // Check cache health
      const cacheHealth = await cacheService.healthCheck();
      expect(cacheHealth.status).toBe('healthy');

      // Check rate limiter health (via stats)
      const rateLimitStats = await rateLimiter.getStats('test-ip');
      expect(rateLimitStats).toHaveProperty('identifier');
      expect(rateLimitStats).toHaveProperty('currentCount');
    });

    it('should track cache hit/miss metrics', async () => {
      // Pre-populate cache
      await cacheService.set('metrics-test-key', { data: 'test' }, 60000);

      // Get existing key (hit)
      await cacheService.get('metrics-test-key');

      // Get non-existent key (miss)
      await cacheService.get('non-existent-key');

      // Check metrics
      const stats = await cacheService.stats();
      expect(stats.hits).toBeGreaterThan(0);
      expect(stats.misses).toBeGreaterThan(0);
    });

    it('should track rate limit metrics', async () => {
      const testIp = '*************';
      const testReq = {
        ...mockReq,
        headers: { 'x-forwarded-for': testIp },
      };

      // Make several requests from the same IP
      for (let i = 0; i < 5; i++) {
        const freshRes = {
          status: vi.fn().mockReturnThis(),
          json: vi.fn().mockReturnThis(),
          setHeader: vi.fn().mockReturnThis(),
          end: vi.fn().mockReturnThis(),
          getHeader: vi.fn(),
          locals: {},
        };
        await rateLimitMiddleware(
          testReq as VercelRequest,
          freshRes as VercelResponse,
          nextFunction
        );
      }

      // Check rate limit stats
      const stats = await rateLimiter.getStats(`ip:${testIp}`);
      expect(stats.currentCount).toBeGreaterThanOrEqual(5);
    });
  });
});
