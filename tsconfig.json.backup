{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@api/*": ["./api/*"]}, "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["src", "api"], "exclude": ["**/*.test.ts", "**/*.spec.ts", "node_modules"], "references": [{"path": "./tsconfig.node.json"}]}