# Test pages - specific redirects first
/react-cdn  /react-cdn-test.html  200
/minimal  /minimal-test.html  200
/debug  /debug-index.html  200
/simple  /simple-index.html  200

# Working app backup (for testing)
/working-app  /working-app.html  200

# SPA fallback for React Router - CRITICAL for client-side routing
/*    /index.html   200

# API routes (if any)
/api/*  /api/:splat  200

# Static assets should be served directly
/assets/*  /assets/:splat  200
/favicon.ico  /favicon.ico  200
/robots.txt  /robots.txt  200
/sitemap.xml  /sitemap.xml  200
