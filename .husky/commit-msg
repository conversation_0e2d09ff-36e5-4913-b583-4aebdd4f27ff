#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "📝 Validating commit message format..."

# Run commitlint to validate commit message
npx --no-install commitlint --edit "$1"
if [ $? -ne 0 ]; then
  echo ""
  echo "❌ Commit message format is invalid!"
  echo ""
  echo "📋 Valid commit message format:"
  echo "   <type>[optional scope]: <description>"
  echo ""
  echo "🏷️  Valid types:"
  echo "   feat:     ✨ A new feature"
  echo "   fix:      🐛 A bug fix"
  echo "   docs:     📚 Documentation only changes"
  echo "   style:    💎 Changes that do not affect the meaning of the code"
  echo "   refactor: 📦 A code change that neither fixes a bug nor adds a feature"
  echo "   perf:     🚀 A code change that improves performance"
  echo "   test:     🚨 Adding missing tests or correcting existing tests"
  echo "   build:    🛠  Changes that affect the build system or external dependencies"
  echo "   ci:       ⚙️  Changes to CI configuration files and scripts"
  echo "   chore:    ♻️  Other changes that don't modify src or test files"
  echo "   revert:   🗑  Reverts a previous commit"
  echo "   security: 🔒 A code change that fixes a security issue"
  echo "   deps:     📦 A change to external dependencies"
  echo "   config:   🔧 A change to configuration files"
  echo ""
  echo "📝 Examples:"
  echo "   feat(auth): add user authentication"
  echo "   fix(api): resolve timeout issue in user endpoint"
  echo "   docs: update installation instructions"
  echo "   style: format code with prettier"
  echo ""
  echo "💡 Tip: Use 'npm run commit' for an interactive commit message builder"
  echo ""
  exit 1
fi

echo "✅ Commit message format is valid!"
