# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
coverage/
.nyc_output/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Generated files
*.tgz
*.tar.gz

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/

# Temporary folders
tmp/
temp/

# Generated documentation
docs/api/

# Compiled binary addons
build/Release/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Ignore specific file types that shouldn't be formatted
*.min.js
*.min.css
*.bundle.js
*.chunk.js

# Ignore generated files
src/types/generated/
api/dist/
public/sw.js

# Ignore large data files
*.csv
*.json.gz
*.xml.gz

# Ignore binary files
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.svg
*.woff
*.woff2
*.ttf
*.eot

# Ignore configuration files that have specific formatting
.github/workflows/*.yml
.github/workflows/*.yaml
docker-compose.yml
docker-compose.yaml

# Ignore markdown files with specific formatting
CHANGELOG.md
LICENSE.md
