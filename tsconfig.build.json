{"extends": "./tsconfig.json", "compilerOptions": {"strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitThis": false, "useUnknownInCatchVariables": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "api/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}