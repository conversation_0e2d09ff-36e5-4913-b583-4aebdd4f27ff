<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Debug Timestamp Converter</title>
    <style>
      body {
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #f9fafb;
      }
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
    </style>
    <script type="module" crossorigin src="/assets/main-DfbmruXB.js"></script>
  </head>
  <body>
    <div id="root">
      <div style="padding: 20px; text-align: center;">
        <h1>Loading Debug App...</h1>
        <p>If this message persists, check the browser console for errors.</p>
      </div>
    </div>
    <script>
      console.log('Debug HTML loaded');
      console.log('Root element:', document.getElementById('root'));
      console.log('Document ready state:', document.readyState);
      
      // 添加全局错误处理
      window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
      });
      
      window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
      });
    </script>
  </body>
</html>
