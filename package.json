{"name": "timestamp-converter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:api": "bash scripts/start-dev.sh", "dev:full": "concurrently \"npm run dev\" \"npm run dev:api\"", "test": "vitest", "test:unit": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "build": "tsc --noEmit --project tsconfig.build.json && vite build", "build:frontend": "tsc && vite build", "build:api": "tsc -p api/tsconfig.json", "build:cloudflare": "tsc --noEmit --project tsconfig.frontend.json && vite build", "deploy:cloudflare": "npm run build:cloudflare && wrangler pages deploy dist", "preview:cloudflare": "wrangler pages dev dist", "test:migration": "node scripts/test-migration.js", "verify:migration": "node scripts/verify-migration.js", "test:api-docs": "node scripts/test-api-docs.js", "monitor:production": "node scripts/monitor-production.js", "test:cache": "node scripts/test-cache.js", "test:production": "node scripts/test-production-optimization.js", "test:api-extensions": "node scripts/test-api-extensions.js", "test:frontend": "node scripts/test-frontend-features.js", "test:unified": "node scripts/test-unified-handlers.js", "typescript:analyze": "node scripts/analyze-typescript.js", "typescript:optimize": "node scripts/optimize-typescript.js", "typescript:fix": "node scripts/fix-typescript-issues.js", "typescript:quickfix": "node scripts/quick-fix-typescript.js", "verify:refactoring": "node scripts/verify-refactoring.js", "verify:implementation": "node scripts/verify-implementation.js", "fix:dark-mode": "node scripts/fix-dark-mode-styles.js", "fix:accessibility": "node scripts/fix-accessibility.js", "fix:dark-mode-comprehensive": "node scripts/fix-dark-mode-comprehensive.js", "fix:color-scheme": "node scripts/fix-color-scheme-and-dropdowns.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "strict-check": "node scripts/strict-mode-check.cjs", "type-check": "tsc --noEmit", "type-check-advanced": "node scripts/advanced-type-check.cjs", "fix-types": "node scripts/fix-type-errors.cjs", "type-errors": "tsc --noEmit --skipLibCheck 2>&1 | grep 'error TS' | wc -l", "type-coverage": "npm run type-check-advanced", "test-csp": "node scripts/test-csp-headers.cjs", "test-security": "npm run test-csp", "test-security-headers": "node scripts/test-security-headers.cjs", "test-security-full": "npm run test-security-headers", "audit-security": "npm run test-security-headers", "test-api-security": "node scripts/test-api-security.cjs", "test-api-auth": "npm run test-api-security", "security-audit": "npm run test-api-security && npm run test-security-headers", "analyze-icons": "node scripts/simple-icon-analysis.cjs", "optimize-icons": "node scripts/manual-icon-optimization.cjs", "icon-audit": "npm run analyze-icons", "analyze": "npx vite-bundle-analyzer dist", "build:analyze": "npm run build && npm run analyze", "analyze-code-splitting": "node scripts/analyze-code-splitting.cjs", "code-splitting-report": "npm run build && npm run analyze-code-splitting", "analyze-images": "node scripts/analyze-image-optimization.cjs", "optimize-images": "node scripts/optimize-images.cjs", "image-optimization-report": "npm run optimize-images && npm run analyze-images", "analyze-cache": "node scripts/analyze-cache-strategy.cjs", "cache-report": "npm run build && npm run analyze-cache", "analyze-bundle": "npm run build && npm run analyze-code-splitting", "ci:quality": "npm run lint && npm run type-check && npm run test", "ci:build": "npm run optimize-images && npm run build", "ci:test": "npm run test:unit && npm run test:integration", "quality-check": "npm run type-check && npm run lint && npm run test:coverage", "preview": "vite preview", "format": "node scripts/format-code.cjs", "format:fix": "node scripts/format-code.cjs --fix", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md,yml,yaml}\"", "format:write": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md,yml,yaml}\"", "quality:analysis": "node scripts/code-quality-analysis.cjs", "quality:report": "node scripts/simple-quality-report.cjs", "quality:full": "npm run format:check && npm run lint && npm run type-check && npm run quality:report", "quality:fix": "echo '⚠️  Use quality:safe-fix instead for safer fixes' && exit 1", "quality:safe-fix": "node scripts/safe-quality-fix.cjs", "quality:gradual-fix": "node scripts/gradual-quality-fix.cjs", "quality:fix-single": "echo 'Usage: npm run quality:fix-single -- <file>' && npx prettier --write", "quality:quick": "npm run quality:report", "commit": "cz", "prepare": "husky install", "test:api": "node scripts/test-apis.js", "test:working": "node test-working-apis.js", "audit": "npm audit", "audit:fix": "npm audit fix", "security:check": "npm audit --audit-level high", "test:error-tracking": "node scripts/test-error-tracking.cjs", "test:performance": "node scripts/test-performance-monitoring.js", "verify:performance": "node scripts/verify-performance-monitoring.cjs", "security:scan": "node scripts/security-scanner.cjs", "security:config": "node scripts/security-config-checker.cjs", "security:full": "npm run security:scan && npm run security:config", "security:quick": "npm audit --audit-level=moderate && npm run security:config", "verify:security": "node scripts/verify-security-automation.cjs"}, "dependencies": {"@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@sentry/node": "^10.3.0", "@sentry/react": "^10.3.0", "@sentry/vite-plugin": "^4.0.2", "@types/jsonwebtoken": "^9.0.10", "@upstash/redis": "^1.35.1", "@vercel/node": "^2.3.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.1.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml,css,scss}": ["prettier --write"]}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.16", "commitizen": "^4.3.1", "concurrently": "^9.2.0", "cors": "^2.8.5", "cz-conventional-changelog": "^3.0.1", "esbuild": "^0.25.8", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.6", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.4", "express": "^4.21.2", "husky": "^9.1.7", "jsdom": "^23.0.1", "lint-staged": "^16.1.5", "postcss": "^8.4.32", "prettier": "^3.6.2", "sharp": "^0.34.3", "tailwindcss": "^3.3.6", "terser": "^5.43.1", "typescript": "^5.2.2", "vercel": "^44.7.3", "vite": "^7.1.1", "vitest": "^3.2.4", "wrangler": "^4.26.0"}, "engines": {"node": ">=18.0.0"}, "overrides": {"lodash": "^4.17.21", "minimist": "^1.2.8", "path-to-regexp": "^6.3.0", "braces": "^3.0.3", "micromatch": "^4.0.8", "merge": "^2.1.1", "shelljs": "^0.8.5", "tar": "^6.2.1", "undici": "^5.29.0"}}